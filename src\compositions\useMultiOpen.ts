import { ref } from 'vue'
import QRCode from 'qrcode'
import {
  encryptPwd,
  getLocalStorage,
  getSessionStorage,
  handlingErrors,

  setLocalStorage,
  setSessionStorage,
} from '~/utils/utils'
import { createOrderApi, getAdditionalServiceApi, loginApi, payApi, setTokenApi } from '~/api/wbl'
import { cookieIsolationManager } from '~/utils/CookieIsolationManager'
import { shouldAutoRefreshOnFailure } from '~/utils/CookieIsolationConfig'
import initGeetest4 from '~/utils/gt'
import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import type {
  AdditionalService,
  AdditionalServiceOptions,
  DeliveryInfo,
  GeetestObj,
  GoodsDetail,
  OrderCallback,
  PayCallback,
} from '~/type'
import { openSelectRoleDialog } from '~/compositions/useSelectRole'
import {
  loginCaptchaId,
  orderCaptchaId,
  orderCaptchaTime,

  orderRetryTimes,
  payRetryDelayTime,
  payRetryTimes,
} from '~/compositions/useSetting'
import { useConcurrencyControl } from '~/compositions/useConcurrencyControl'

// 当前选择框
export const currSelect = ref('')
// 是否是角色多开
export const isRoleMultiOpen = ref(false)
// 是否是外观多开
export const isSkinMultiOpen = ref(false)
export const isShowLoginDialog = ref(false)
export const currQrcode = ref('')
const account = ref('')
const pwd = ref('')
export function useLoginDialog() {
  return {
    account,
    pwd,
  }
}

export const zfbUrls = ref<string[]>([])

function getLocalAccountTokens() {
  const list = getSessionStorage('accountTokenList', []) as AccountToken[]
  const filterList = list.filter(item => item.expire > Date.now())
  if (filterList.length !== list.length)
    setSessionStorage('accountTokenList', filterList)
  if (zfbUrls.value.length !== filterList.length)
    zfbUrls.value = Array.from({ length: filterList.length }).fill('') as string[]
  return filterList
}
// 多开验证码对象
export const multiGeetestObj = ref< GeetestObj[] >([])
interface LocalGeetestObj {
  geetestObj: GeetestObj
  expire: number
}
function getLocalGeetest() {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item.expire > Date.now())
  if (filterList.length !== list.length)
    setSessionStorage('multiGeetestObj', filterList)
  multiGeetestObj.value = filterList.map(item => item.geetestObj)
}
getLocalGeetest()

function setLocalGeetest(geetestObj: GeetestObj) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  list.push({ geetestObj, expire: Date.now() + 60 * orderCaptchaTime * 1000 })
  setSessionStorage('multiGeetestObj', list)
}

function removeLocalGeetestByCaptchaId(captchaId: string) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item?.geetestObj?.captcha_id !== captchaId)
  setSessionStorage('multiGeetestObj', filterList)
}

function getGeetestObj() {
  if (multiGeetestObj.value.length === 0) {
    errNotify(`验证码数量不足。`)
  }
  else {
    const geetestObj = multiGeetestObj.value.pop() as GeetestObj
    if (geetestObj) {
      removeLocalGeetestByCaptchaId(geetestObj.captcha_id)
      return geetestObj
    }
  }
  return null
}

const currLoginInfo = ref()
interface AccountToken {
  account: string
  token: string
  expire: number
  separationServiceFee?: number
  isSelectedRole?: boolean
  roleInfo?: string
}
// 多开账号token列表
export const accountTokenList = ref<AccountToken[]>(getLocalAccountTokens())
// 是否打开登录框
export const isOpenLoginDialog = ref(false)

export async function selectRole(account: AccountToken) {
  if (!account?.isSelectedRole)
    openSelectRoleDialog(account.token)
  else
    successNotify('该账号已选择角色')
}

export const currAccount = ref(`账号：${getSessionStorage('account', '首次登录账号')}`)

/**
 * 处理添加账号后的页面刷新
 * 简化方案：直接刷新页面解决Cookie污染问题
 */
async function handleAccountAddRefresh() {
  try {
    // 检查是否应该自动刷新
    const autoRefresh = shouldAutoRefreshOnFailure('ADD_ACCOUNT')

    if (autoRefresh) {
      console.log('添加新账号完成，准备刷新页面解决Cookie污染问题')

      // 给用户看到成功提示的时间
      const refreshDelay = 1500 // 1.5秒延迟

      // 提示用户即将刷新
      warningNotify('账号添加成功，即将刷新页面以确保状态同步')

      // 延迟刷新，让用户看到成功提示
      setTimeout(() => {
        console.log('执行页面刷新以解决Cookie污染问题')
        window.location.reload()
      }, refreshDelay)
    }
    else {
      // 如果配置为不自动刷新，则只提示用户
      warningNotify('账号添加成功，建议刷新页面以确保状态同步')
    }
  }
  catch (error) {
    console.error('处理账号添加刷新失败:', error)
    // 如果出错，直接刷新页面作为兜底
    warningNotify('账号添加完成，即将刷新页面')
    setTimeout(() => window.location.reload(), 1000)
  }
}

export function setToken(token: string, account?: string) {
  // 如果token在列表中已存在， 则不设置
  if (token === '')
    return
  const index = accountTokenList.value.findIndex(item => item.token === token)
  setSessionStorage('currToken', token)
  if (index === -1) {
    setLocalStorage('token', token)
    if (account) {
      setSessionStorage('account', account)
      currAccount.value = `账号：${account}`
    }
  }
}

async function login(captcha: GeetestObj) {
  try {
    // 使用标准登录API
    const res = await loginApi(currLoginInfo.value.account, encryptPwd(currLoginInfo.value.password), captcha)
    if (res.code === 1) {
      account.value = ''
      pwd.value = ''
      isShowLoginDialog.value = false
      // 先检查是否已经登录过， 如果已经登录过， 则替换token及过期时间
      const index = accountTokenList.value.findIndex(item => item.account === currLoginInfo.value.account)
      if (index !== -1) {
        accountTokenList.value[index].token = res.data.ts_session_id
        accountTokenList.value[index].expire = (new Date().getTime() + 3600 * 1000)
        zfbUrls.value[index] = ''
      }
      else {
        accountTokenList.value.push({ account: currLoginInfo.value.account, token: res.data.ts_session_id, expire: (new Date().getTime() + 3600 * 1000) })
        zfbUrls.value.push('')
      }
      isRoleMultiOpen.value = false
      isSkinMultiOpen.value = false
      setSessionStorage('accountTokenList', accountTokenList.value)
      successNotify(`账号 [ ${currLoginInfo.value.account} ] 登录成功`)

      // 简化方案：添加新账号后直接刷新页面解决Cookie污染问题
      await handleAccountAddRefresh()
    }
    else {
      errNotify(res.msg)
    }
  }
  catch (error) {
    console.error('账号登录过程中出现错误:', error)
    errNotify('账号登录失败，请重试')
  }
}

function loginEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(async () => {
    const captcha = captchaObj.getValidate() as GeetestObj
    await login(captcha)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}
export const loadAccountCallBack = ref<Function | null>(null)

function handlerEmbedSuccess(geetestTemp: GeetestObj) {
  multiGeetestObj.value.push(geetestTemp)
  setLocalGeetest(geetestTemp)
  if (multiGeetestObj.value.length < accountTokenList.value.length + 1) {
    loadAccountCaptcha()
  }
  else {
    if (loadAccountCallBack.value) {
      loadAccountCallBack.value()
      loadAccountCallBack.value = null
    }
    if (currSelect.value === 'role')
      isRoleMultiOpen.value = true
    else if (currSelect.value === 'skin')
      isSkinMultiOpen.value = true
    currSelect.value = ''
  }
}
function handlerEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(() => {
    const geetestTemp = captchaObj.getValidate() as GeetestObj
    handlerEmbedSuccess(geetestTemp)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}

export async function loadAccountCaptcha() {
  initGeetest4({ product: 'bind', captchaId: orderCaptchaId }, handlerEmbed)
  await new Promise(resolve => setTimeout(resolve, 1000))
}

export function loginWbl(account: string, password: string) {
  currLoginInfo.value = { account, password }
  if (account && password)
    initGeetest4({ product: 'bind', captchaId: loginCaptchaId }, loginEmbed)
  else
    warningNotify('请输入账号密码')
}

function showQrcode(str: string, index: number) {
  const opts = {
    errorCorrectionLevel: 'Q',
    quality: 1,
    margin: 1,
    width: 300,
    color: {
      dark: '#1688ff',
      light: '#fff',
    },
  }
  // 将二维码渲染到 canvas 中，然后将 canvas 添加到目标元素
  QRCode.toDataURL(str, opts, (err: any, url: string) => {
    if (err) {
      errNotify('生成二维码失败')
      return
    }
    if (index === -1)
      currQrcode.value = url
    else
      zfbUrls.value[index] = url
  })
}

async function pay(orderId: string, type: number, token: string, index: number) {
  let retryCount = 0
  let res
  let errorOccurred = false

  do {
    try {
      if (retryCount > 0)
        await new Promise(resolve => setTimeout(resolve, payRetryDelayTime))

      res = await payApi(orderId, type, token)
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
    }
    catch (error) {
    // 如果捕获到错误，设置 errorOccurred 为 true
      errorOccurred = true
    }
    retryCount++
  } while ((errorOccurred || res?.code !== 1) && retryCount <= payRetryTimes.value)
  if (res?.code !== 1) {
    errNotify(res.msg)
  }
  else {
    const pay_attach = (res.data as PayCallback).pay_attach
    showQrcode(pay_attach, index)
  }
}

export async function multiAdditionalService(orderId: string) {
  // 网页登录用户的分离费
  const originToken = getLocalStorage('token')
  const originRes = await getAdditionalServiceApi(orderId, originToken)
  const originData = originRes.data.list as AdditionalService[]
  const originSeparationServiceFee = originData.find(item => item.name === 'separation_service')?.value
  setSessionStorage('originSeparationServiceFee', originSeparationServiceFee)
  // 插件登录用户的分离费
  for (const item of accountTokenList.value) {
    const index = accountTokenList.value.indexOf(item)
    if (item.separationServiceFee !== undefined)
      continue
    const res = await getAdditionalServiceApi(orderId, item.token)
    if (res.code === 1) {
      const data = res.data.list as AdditionalService[]
      accountTokenList.value[index].separationServiceFee = data.find(item => item.name === 'separation_service')?.value
    }
    else {
      errNotify(`账号 [ ${item.account} ] 获取角色分离费失败：${res.msg}`)
    }
  }
  setSessionStorage('accountTokenList', accountTokenList.value)
}

function getAdditionalInfoByToken(additionalServiceSum: number, additionalService: AdditionalService[] | undefined, currAccount?: AccountToken) {
  let tempAdditionalServiceSum = JSON.parse(JSON.stringify(additionalServiceSum)) as number
  const tempAdditionalService = JSON.parse(JSON.stringify(additionalService)) as AdditionalService[]
  const tempSepFee = additionalService?.find(item => item.name === 'separation_service')?.value || 0
  if (!currAccount) {
    const originFee = getSessionStorage('originSeparationServiceFee')
    if (tempSepFee !== originFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + originFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = originFee
    }
  }
  else if (currAccount.separationServiceFee !== undefined && tempAdditionalService !== undefined) {
    if (tempSepFee !== currAccount.separationServiceFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + currAccount.separationServiceFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = currAccount.separationServiceFee
    }
  }
  return { tempAdditionalServiceSum, tempAdditionalService }
}

const { executeWithConcurrencyControl, executeBatch } = useConcurrencyControl()

// 使用并发控制的订单创建函数
async function createOrderAndNotifyWithConcurrency(type: number, row: GoodsDetail, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, currAccount: AccountToken, i: number, deliveryInfo?: DeliveryInfo) {
  return await executeWithConcurrencyControl(
    async () => {
      const geetestObj = getGeetestObj() as GeetestObj
      if (!geetestObj)
        throw new Error('验证码数量不足，创建订单失败。')

      const res = await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, currAccount.token, deliveryInfo)

      if (res.code === 1) {
        successNotify(`账号 [ ${currAccount.account} ] 订单创建成功`)
        const orderId = (res.data as OrderCallback).order_id
        await pay(orderId, type, currAccount.token, i)
        return res
      }
      else {
        throw new Error(`账号 [ ${currAccount.account} ] 订单创建失败：${res.msg}`)
      }
    },
    {
      priority: 2,
      maxRetries: orderRetryTimes.value,
      id: `account_${currAccount.account}_order_${i}`,
    },
  )
}

export async function multiCreateOrder(row: GoodsDetail, type: number, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, deliveryInfo?: DeliveryInfo) {
  const geetestObj = getGeetestObj() as GeetestObj
  if (!geetestObj) {
    errNotify('验证码数量不足，创建订单失败。')
    return
  }

  // 启动Cookie隔离模式，防止多开抢号过程中Cookie冲突
  console.log('开始多开抢号，启动Cookie隔离模式')
  cookieIsolationManager.startIsolation()

  const token = getLocalStorage('token')

  // 使用并发控制执行主账号订单创建
  try {
    const mainOrderResult = await executeWithConcurrencyControl(
      async () => {
        if (type === 2) {
          const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService)
          return await createOrderApi(row, geetestObj, type, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, token)
        }
        else {
          return await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, token, deliveryInfo)
        }
      },
      { priority: 3, maxRetries: orderRetryTimes.value, id: 'main_account_order' },
    )

    if (mainOrderResult?.code === 1) {
      successNotify(`当前账号订单创建成功`)
      const orderId = (mainOrderResult.data as OrderCallback).order_id
      await pay(orderId, type, token, -1)
    }
    else {
      errNotify(`当前账号订单创建失败：${mainOrderResult.msg}`)
    }
  }
  catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    errNotify(`当前账号订单创建失败：${errorMessage}`)
  }

  // 为多开账号创建订单请求数组
  const multiAccountRequests = accountTokenList.value.map((currAccount, i) => {
    return async () => {
      if (type === 2) {
        const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService, currAccount)
        return await createOrderAndNotifyWithConcurrency(type, row, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, currAccount, i)
      }
      else if (type === 3) {
        return await createOrderAndNotifyWithConcurrency(type, row, additionalServiceSum, transferServiceObj, additionalService, currAccount, i, deliveryInfo)
      }
    }
  })

  // 使用批量执行，智能控制并发
  try {
    await executeBatch(multiAccountRequests, {
      batchSize: 2, // 每批2个请求
      priority: 2,
      maxRetries: orderRetryTimes.value,
    })
  }
  catch (error) {
    console.error('多开账号订单创建过程中出现错误:', error)
  }
  finally {
    // 无论成功还是失败，都要结束Cookie隔离模式
    console.log('多开抢号完成，结束Cookie隔离模式')
    cookieIsolationManager.endIsolation()
  }

  await setTokenApi()
}
