/**
 * Cookie隔离配置管理
 * 用于配置不同场景下的Cookie隔离策略
 */

export interface CookieIsolationConfig {
  // 是否启用Cookie隔离
  enabled: boolean
  // 隔离超时时间（毫秒）
  timeoutMs: number
  // 是否在失败时自动刷新页面
  autoRefreshOnFailure: boolean
  // 刷新延迟时间（毫秒）
  refreshDelayMs: number
  // 是否显示详细日志
  verboseLogging: boolean
}

// 默认配置
const DEFAULT_CONFIG: CookieIsolationConfig = {
  enabled: true,
  timeoutMs: 30 * 60 * 1000, // 30分钟
  autoRefreshOnFailure: false, // 默认不自动刷新，让用户选择
  refreshDelayMs: 2000, // 2秒延迟
  verboseLogging: true
}

// 不同场景的配置
export const ISOLATION_CONFIGS = {
  // 多开抢号场景
  MULTI_ORDER: {
    ...DEFAULT_CONFIG,
    autoRefreshOnFailure: false, // 抢号时不自动刷新
    verboseLogging: true
  } as CookieIsolationConfig,

  // 添加新账号场景
  ADD_ACCOUNT: {
    ...DEFAULT_CONFIG,
    timeoutMs: 5 * 60 * 1000, // 5分钟超时
    autoRefreshOnFailure: true, // 添加账号失败时可以自动刷新
    refreshDelayMs: 2000,
    verboseLogging: true
  } as CookieIsolationConfig,

  // 账号切换场景
  SWITCH_ACCOUNT: {
    ...DEFAULT_CONFIG,
    timeoutMs: 2 * 60 * 1000, // 2分钟超时
    autoRefreshOnFailure: true, // 切换失败时自动刷新
    refreshDelayMs: 1000,
    verboseLogging: false
  } as CookieIsolationConfig
}

/**
 * 获取指定场景的配置
 */
export function getIsolationConfig(scenario: keyof typeof ISOLATION_CONFIGS): CookieIsolationConfig {
  return ISOLATION_CONFIGS[scenario] || DEFAULT_CONFIG
}

/**
 * 检查是否应该启用Cookie隔离
 */
export function shouldEnableIsolation(scenario: keyof typeof ISOLATION_CONFIGS): boolean {
  const config = getIsolationConfig(scenario)
  return config.enabled
}

/**
 * 检查是否应该在失败时自动刷新
 */
export function shouldAutoRefreshOnFailure(scenario: keyof typeof ISOLATION_CONFIGS): boolean {
  const config = getIsolationConfig(scenario)
  return config.autoRefreshOnFailure
}
