<script setup lang="ts">
import { computed, nextTick } from 'vue'
import CardHeader from '~/components/CardHeader.vue'
import {
  accountTokenList,
  currAccount,
  currSelect,
  isOpenLoginDialog,
  isRoleMultiOpen,
  isShowLoginDialog,
  isSkinMultiOpen,
  loadAccountCaptcha,
  multiGeetestObj,
  selectRole,
  zfbUrls,
} from '~/compositions/useMultiOpen'
import LoginDialog from '~/components/dialogs/LoginDialog.vue'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { getLocalStorage, getSessionStorage, setSessionStorage } from '~/utils/utils'
import IconBtn from '~/components/IconBtn.vue'
import { logoutApi, setTokenApi } from '~/api/wbl'
import { useAccountSwitch } from '~/compositions/useAccountSwitch'
import { useConfirmation } from '~/compositions/useConfirmation'

function onchange(type: 'role' | 'skin') {
  currSelect.value = ''
  if (accountTokenList.value.length === 0) {
    errNotify('请先添加账号')
    isSkinMultiOpen.value = false
    isRoleMultiOpen.value = false
    return
  }
  if (type === 'skin') {
    const isOpenSkinMultiOpen = accountTokenList.value.every(item => item?.isSelectedRole)
    if (!isOpenSkinMultiOpen) {
      errNotify('请先完成所有账号的角色选择')
      isSkinMultiOpen.value = false
      isRoleMultiOpen.value = false
      return
    }
  }

  if (multiGeetestObj.value.length >= (accountTokenList.value.length + 1)) {
    if (type === 'role')
      isSkinMultiOpen.value = false
    else
      isRoleMultiOpen.value = false
  }
  else {
    if (type === 'role' && !isRoleMultiOpen.value)
      return
    if (type === 'skin' && !isSkinMultiOpen.value)
      return
    errNotify('至少完成账号等量的验证码输入后才能开启多开')
    currSelect.value = type
    loadAccountCaptcha()
    isRoleMultiOpen.value = false
    isSkinMultiOpen.value = false
  }
}

async function removeAccountToken(account: string) {
  // 显示确认对话框
  const confirmed = await confirmRemoveAccount(account)
  if (!confirmed)
    return

  try {
    const token = accountTokenList.value.find(item => item.account === account)?.token as string

    // 先调用登出API
    if (token)
      await logoutApi(token)

    // 从列表中移除账号
    accountTokenList.value = accountTokenList.value.filter(item => item.account !== account)
    zfbUrls.value.pop()

    // 更新本地存储
    setSessionStorage('accountTokenList', accountTokenList.value)
    await setTokenApi()

    successNotify(`账号 ${account} 已成功移除`)
  }
  catch (error) {
    console.error('移除账号失败:', error)
    errNotify('移除账号失败，请重试')
  }
}

const isCurrToken = computed(() => {
  const tempToken = getLocalStorage('token')
  const currToken = getSessionStorage('currToken')
  const index = accountTokenList.value.findIndex(item => item.token === tempToken)
  return index === -1 && tempToken === currToken
})

function isActive(token: string) {
  return token === getSessionStorage('currToken')
}

const { switchAccountSafely, isSwitching, switchProgress } = useAccountSwitch()
const { confirmAccountSwitch, confirmRemoveAccount } = useConfirmation()

async function changeAccount(token?: string) {
  // 获取当前账号和目标账号信息
  const currentAccount = getSessionStorage('account', '当前账号')
  let targetAccount = '主账号'

  if (token) {
    const account = accountTokenList.value.find(item => item.token === token)
    targetAccount = account?.account || '未知账号'
  }

  // 如果是相同账号，直接返回
  if (currentAccount === targetAccount) {
    errNotify('已经是当前账号了')
    return
  }

  // 对于快速切换，可以跳过确认（按住Ctrl键时）
  const skipConfirmation = false // 这里可以根据需要添加快速切换逻辑

  if (!skipConfirmation) {
    // 显示确认对话框
    const confirmed = await confirmAccountSwitch(currentAccount, targetAccount)
    if (!confirmed)
      return
  }

  // 使用新的安全账号切换机制
  const success = await switchAccountSafely(token)

  // 如果切换成功，进行状态同步
  if (success) {
    try {
      // 等待一小段时间确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 200))

      // 更新本地状态
      await updateLocalStates()

      // 显示成功提示
      successNotify(`已切换到账号：${targetAccount}`)

      console.log('账号切换完成，即将刷新页面以确保状态同步')

      // 在UserScript环境中，需要页面刷新来确保所有状态完全同步
      setTimeout(() => {
        window.location.reload()
      }, 500) // 500ms延迟让用户看到成功提示
    }
    catch (error) {
      console.error('状态更新失败:', error)
      errNotify('账号切换后状态同步失败，请手动刷新页面')
    }
  }
}

// 智能更新本地状态，避免页面刷新
async function updateLocalStates() {
  try {
    // 1. 更新当前账号显示
    const baseInfo = getSessionStorage('baseInfo')
    if (baseInfo?.account)
      currAccount.value = `账号：${baseInfo.account}`

    // 2. 触发账号切换事件，让其他组件知道账号已切换
    window.dispatchEvent(new CustomEvent('accountSwitched', {
      detail: {
        newAccount: baseInfo?.account,
        timestamp: Date.now(),
      },
    }))

    // 3. 等待DOM更新
    await nextTick()

    // 本地状态更新完成
  }
  catch (error) {
    console.error('本地状态更新失败:', error)
    throw error
  }
}
</script>

<template>
  <q-dialog v-model="isOpenLoginDialog" persistent transition-show="scale" transition-hide="scale">
    <q-card min-w-700px>
      <CardHeader title="多开账号" @close="isOpenLoginDialog = false" />
      <q-card-section>
        <q-list bordered separator class="rounded-borders">
          <q-item v-for="item in accountTokenList" :key="item.account" v-ripple clickable :active="isActive(item.token)" active-class="text-green" :disable="isSwitching">
            <q-item-section @click="changeAccount(item.token)">
              <q-item-label lines="1">
                账号：{{ item.account }}
              </q-item-label>
              <q-item-label v-if="isSwitching && isActive(item.token) && switchProgress" caption class="text-orange">
                {{ switchProgress }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div flex="~ row items-center gap-2">
                <q-spinner v-if="isSwitching && isActive(item.token)" color="orange" size="sm" />
                <div flex="~ col justify-end " @click="selectRole(item)">
                  <div>
                    <template v-if="Date.now() < item.expire">
                      <q-icon size="sm" name="i-pixelarticons-check" color="green" />
                      <span ml-10px class="text-green">已登录，失效时间：{{ new Date(item.expire).toLocaleString() }}</span>
                    </template>
                    <template v-else>
                      <q-icon size="sm" name="i-pixelarticons-close" color="red" />
                      <span ml-10px class="text-red">已失效</span>
                    </template>
                  </div>
                  <div>
                    <template v-if="item?.isSelectedRole">
                      <span>当前角色：{{ item?.roleInfo }}</span>
                    </template>
                    <template v-else>
                      <span>单击此处选择角色</span>
                    </template>
                  </div>
                </div>
                <IconBtn color="red" icon="i-pixelarticons-trash" tooltip="退出登录" flat :disable="isSwitching" @click="removeAccountToken(item.account)" />
              </div>
            </q-item-section>
          </q-item>
          <q-item v-ripple clickable active-class="text-green" :active="isCurrToken" :disable="isSwitching">
            <q-item-section @click="changeAccount()">
              <q-item-label lines="1">
                {{ currAccount }}
              </q-item-label>
              <q-item-label v-if="isSwitching && switchProgress" caption class="text-orange">
                {{ switchProgress }}
              </q-item-label>
            </q-item-section>
            <q-item-section v-if="isSwitching" side>
              <q-spinner color="orange" size="sm" />
            </q-item-section>
            <q-item-section side>
              <div>
                <q-icon size="sm" name="i-pixelarticons-check" color="green" />
                <span ml-10px class="text-green">已登录</span>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
      <q-card-section>
        <div text-gray>
          如果多开抢号/抢外观结束后，账号自动切换到别的号上，可以单击该界面上面左边的 “账号：xxx” 来切换账号。
        </div>
        <!-- 多开抢外观特殊提示 -->
        <div v-if="isSkinMultiOpen" class="q-mt-md q-pa-md bg-orange-1 rounded-borders">
          <div class="text-subtitle2 text-orange-8 q-mb-sm">
            <q-icon name="i-pixelarticons-info" class="q-mr-xs" />
            多开抢外观模式说明
          </div>
          <div class="text-body2 text-orange-7">
            • 所有账号的收获方式已统一设置为"万宝楼收货"
            <br>
            • 外观商品将发送到各账号的万宝楼，可随时提取
            <br>
            • 这样设置可以确保所有账号都能正确收到外观商品
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <span :class="multiGeetestObj.length >= accountTokenList.length + 1 ? 'text-green' : 'text-red'">当前验证码：{{ multiGeetestObj.length }}/{{ accountTokenList.length + 1 }}</span>
        <q-checkbox v-model="isSkinMultiOpen" ml-10px color="teal" label="多开抢外观" @update:model-value="onchange('skin')" />
        <q-checkbox v-model="isRoleMultiOpen" ml-10px color="teal" label="多开抢号" @update:model-value="onchange('role')" />
        <q-space />
        <q-btn label="输入验证码" color="teal" @click="loadAccountCaptcha" />
        <q-btn label="添加账号" color="teal" @click="isShowLoginDialog = true" />
      </q-card-actions>
    </q-card>
    <LoginDialog v-model="isShowLoginDialog" />
  </q-dialog>
</template>

<style scoped>

</style>
