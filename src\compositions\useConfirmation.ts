import { Dialog } from 'quasar'

/**
 * 显示确认对话框
 */
export function showConfirmation(options: {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'info' | 'warning' | 'error' | 'success'
  persistent?: boolean
}): Promise<boolean> {
  return new Promise((resolve) => {
    const {
      title,
      message,
      confirmText = '确认',
      cancelText = '取消',
      type = 'info',
      persistent = false,
    } = options

    try {
      // 根据类型设置图标和颜色
      const typeConfig = {
        info: { icon: 'i-pixelarticons-info', color: 'blue' },
        warning: { icon: 'i-pixelarticons-warning', color: 'orange' },
        error: { icon: 'i-pixelarticons-close', color: 'red' },
        success: { icon: 'i-pixelarticons-check', color: 'green' },
      }

      // 检查Dialog是否可用
      if (typeof Dialog === 'undefined' || typeof Dialog.create !== 'function') {
        console.warn('Quasar Dialog不可用，使用浏览器原生确认框')
        // 回退到浏览器原生确认框
        // eslint-disable-next-line no-alert
        const result = window.confirm(`${title}\n\n${message.replace(/<[^>]*>/g, '')}`)
        resolve(result)
        return
      }

      Dialog.create({
        title,
        message,
        persistent,
        ok: {
          label: confirmText,
          color: typeConfig[type].color,
          flat: false,
        },
        cancel: {
          label: cancelText,
          color: 'grey',
          flat: true,
        },
        class: `confirmation-dialog confirmation-${type}`,
        html: true,
      }).onOk(() => {
        resolve(true)
      }).onCancel(() => {
        resolve(false)
      })
    }
    catch (error) {
      console.error('Dialog创建失败:', error)
      // 回退到浏览器原生确认框
      // eslint-disable-next-line no-alert
      const result = window.confirm(`${title}\n\n${message.replace(/<[^>]*>/g, '')}`)
      resolve(result)
    }
  })
}

/**
 * 多开抢号确认
 */
export async function confirmMultiOpenRob(accountCount: number, goodsName: string): Promise<boolean> {
  return showConfirmation({
    title: '确认多开抢号',
    message: `
      <div class="confirmation-content">
        <div class="confirmation-item">
          <strong>商品名称：</strong>${goodsName}
        </div>
        <div class="confirmation-item">
          <strong>参与账号数：</strong>${accountCount} 个账号
        </div>
        <div class="confirmation-warning">
          <div class="warning-title">⚠️ 重要提醒：</div>
          <ul class="warning-list">
            <li>多开抢号将同时为 ${accountCount} 个账号创建订单</li>
            <li>每个账号都需要单独支付，请确保有足够的资金</li>
            <li>请确保所有账号的验证码都已完成</li>
            <li>一旦开始，无法中途取消，请谨慎操作</li>
          </ul>
        </div>
      </div>
    `,
    confirmText: '确认开始抢号',
    cancelText: '取消',
    type: 'warning',
    persistent: true,
  })
}

/**
 * 账号切换确认（简化版）
 */
export async function confirmAccountSwitch(fromAccount: string, toAccount: string): Promise<boolean> {
  return showConfirmation({
    title: '切换账号',
    message: `确认要从 <strong>${fromAccount}</strong> 切换到 <strong>${toAccount}</strong> 吗？`,
    confirmText: '切换',
    cancelText: '取消',
    type: 'info',
  })
}

/**
 * 删除账号确认
 */
export async function confirmRemoveAccount(accountName: string): Promise<boolean> {
  return showConfirmation({
    title: '确认删除账号',
    message: `
      <div class="confirmation-content">
        <div class="confirmation-item">
          <strong>要删除的账号：</strong>${accountName}
        </div>
        <div class="confirmation-warning">
          <div class="warning-title">⚠️ 注意：</div>
          <ul class="warning-list">
            <li>删除后该账号将从多开列表中移除</li>
            <li>正在进行的抢号操作不会受到影响</li>
            <li>如需重新使用该账号，需要重新登录</li>
          </ul>
        </div>
      </div>
    `,
    confirmText: '确认删除',
    cancelText: '取消',
    type: 'error',
    persistent: true,
  })
}

/**
 * 批量操作确认
 */
export async function confirmBatchOperation(
  operation: string,
  itemCount: number,
  itemType: string = '项目',
): Promise<boolean> {
  return showConfirmation({
    title: `确认批量${operation}`,
    message: `
      <div class="confirmation-content">
        <div class="confirmation-item">
          <strong>操作类型：</strong>${operation}
        </div>
        <div class="confirmation-item">
          <strong>影响数量：</strong>${itemCount} 个${itemType}
        </div>
        <div class="confirmation-warning">
          <div class="warning-title">⚠️ 提醒：</div>
          <div>批量操作将同时处理多个${itemType}，请确认无误后继续。</div>
        </div>
      </div>
    `,
    confirmText: `确认${operation}`,
    cancelText: '取消',
    type: 'warning',
  })
}

/**
 * 危险操作确认（需要二次确认）
 */
export async function confirmDangerousOperation(
  operation: string,
  description: string,
  consequences: string[],
): Promise<boolean> {
  // 第一次确认
  const firstConfirm = await showConfirmation({
    title: `确认${operation}`,
    message: `
      <div class="confirmation-content">
        <div class="confirmation-item">
          <strong>操作描述：</strong>${description}
        </div>
        <div class="confirmation-warning">
          <div class="warning-title">⚠️ 警告：此操作可能导致以下后果：</div>
          <ul class="warning-list">
            ${consequences.map(item => `<li>${item}</li>`).join('')}
          </ul>
        </div>
      </div>
    `,
    confirmText: '我了解风险，继续',
    cancelText: '取消',
    type: 'error',
    persistent: true,
  })

  if (!firstConfirm)
    return false

  // 二次确认
  return showConfirmation({
    title: '最终确认',
    message: `
      <div class="confirmation-content">
        <div class="confirmation-final">
          <strong>请再次确认您要执行：${operation}</strong>
        </div>
        <div class="confirmation-note">
          此操作不可撤销，请谨慎考虑。
        </div>
      </div>
    `,
    confirmText: '最终确认',
    cancelText: '取消',
    type: 'error',
    persistent: true,
  })
}

/**
 * 快速确认（用于常见操作）
 */
export async function quickConfirm(message: string): Promise<boolean> {
  return showConfirmation({
    title: '确认操作',
    message,
    confirmText: '确认',
    cancelText: '取消',
    type: 'info',
  })
}

export function useConfirmation() {
  return {
    showConfirmation,
    confirmMultiOpenRob,
    confirmAccountSwitch,
    confirmRemoveAccount,
    confirmBatchOperation,
    confirmDangerousOperation,
    quickConfirm,
  }
}

// 全局样式（需要在主应用中引入）
export const confirmationStyles = `
.confirmation-dialog .q-dialog__inner {
  padding: 20px;
}

.confirmation-content {
  font-size: 14px;
  line-height: 1.5;
}

.confirmation-item {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.confirmation-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.confirmation-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
}

.confirmation-warning.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.warning-title {
  font-weight: 600;
  color: #856404;
  margin-bottom: 8px;
}

.warning-list {
  margin: 0;
  padding-left: 20px;
}

.warning-list li {
  margin-bottom: 4px;
}

.confirmation-note {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
  color: #0066cc;
}

.confirmation-final {
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}
`
