/**
 * Cookie隔离管理器
 * 解决多开抢号过程中Cookie冲突问题
 */

import { type CookieIsolationConfig, getIsolationConfig, shouldEnableIsolation } from './CookieIsolationConfig'
import { getSessionStorage, setSessionStorage } from '~/utils/utils'

// 主账号Cookie备份
interface CookieBackup {
  ts_session_id: string
  ts_session_id_: string
  m_gray_switch: string
  m_gray_switch_: string
  timestamp: number
}

// Cookie隔离状态
interface IsolationState {
  isIsolated: boolean
  mainAccountBackup: CookieBackup | null
  currentIsolatedToken: string | null
  isolationStartTime: number
}

class CookieIsolationManager {
  private state: IsolationState = {
    isIsolated: false,
    mainAccountBackup: null,
    currentIsolatedToken: null,
    isolationStartTime: 0,
  }

  /**
   * 开始Cookie隔离模式
   * 在多开抢号开始前调用，备份主账号Cookie状态
   */
  startIsolation(): void {
    if (this.state.isIsolated) {
      console.warn('Cookie隔离已经启动，跳过重复启动')
      return
    }

    try {
      // 备份当前主账号的Cookie状态
      this.backupMainAccountCookies()

      this.state.isIsolated = true
      this.state.isolationStartTime = Date.now()

      console.log('Cookie隔离模式已启动')
    }
    catch (error) {
      console.error('启动Cookie隔离失败:', error)
    }
  }

  /**
   * 结束Cookie隔离模式
   * 在多开抢号完成后调用，恢复主账号Cookie状态
   */
  endIsolation(): void {
    if (!this.state.isIsolated) {
      console.warn('Cookie隔离未启动，跳过结束操作')
      return
    }

    try {
      // 恢复主账号Cookie状态
      this.restoreMainAccountCookies()

      this.state.isIsolated = false
      this.state.currentIsolatedToken = null
      this.state.isolationStartTime = 0

      console.log('Cookie隔离模式已结束，主账号状态已恢复')
    }
    catch (error) {
      console.error('结束Cookie隔离失败:', error)
    }
  }

  /**
   * 备份主账号Cookie
   */
  private backupMainAccountCookies(): void {
    try {
      // 从当前浏览器Cookie中读取主账号信息
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        if (key && value)
          acc[key] = value

        return acc
      }, {} as Record<string, string>)

      // 也从sessionStorage中获取当前token信息
      const currentToken = getSessionStorage('currToken') || getSessionStorage('token')

      this.state.mainAccountBackup = {
        ts_session_id: cookies.ts_session_id || currentToken || '',
        ts_session_id_: cookies.ts_session_id_ || currentToken || '',
        m_gray_switch: cookies.m_gray_switch || '1',
        m_gray_switch_: cookies.m_gray_switch_ || '1',
        timestamp: Date.now(),
      }

      // 将备份信息存储到sessionStorage中，防止页面刷新丢失
      setSessionStorage('mainAccountCookieBackup', this.state.mainAccountBackup)

      console.log('主账号Cookie已备份:', this.state.mainAccountBackup)
    }
    catch (error) {
      console.error('备份主账号Cookie失败:', error)
    }
  }

  /**
   * 恢复主账号Cookie
   */
  private restoreMainAccountCookies(): void {
    try {
      // 优先从内存中获取备份，如果没有则从sessionStorage中获取
      let backup = this.state.mainAccountBackup
      if (!backup)
        backup = getSessionStorage('mainAccountCookieBackup')

      if (!backup) {
        console.warn('没有找到主账号Cookie备份，无法恢复')
        return
      }

      // 使用GM_cookie API恢复主账号Cookie
      if (typeof GM_cookie !== 'undefined') {
        // 设置主账号Cookie
        GM_cookie.set({
          name: 'ts_session_id',
          value: backup.ts_session_id,
          domain: '.seasunwbl.com',
          path: '/',
          httpOnly: true,
        })

        GM_cookie.set({
          name: 'ts_session_id_',
          value: backup.ts_session_id_,
          domain: '.seasunwbl.com',
          path: '/',
          httpOnly: true,
        })

        GM_cookie.set({
          name: 'm_gray_switch',
          value: backup.m_gray_switch,
          domain: '.seasunwbl.com',
          path: '/',
        })

        GM_cookie.set({
          name: 'm_gray_switch_',
          value: backup.m_gray_switch_,
          domain: '.seasunwbl.com',
          path: '/',
        })

        console.log('主账号Cookie已恢复')
      }
      else {
        console.warn('GM_cookie API不可用，无法恢复Cookie')
      }

      // 清理备份信息
      setSessionStorage('mainAccountCookieBackup', null)
      this.state.mainAccountBackup = null
    }
    catch (error) {
      console.error('恢复主账号Cookie失败:', error)
    }
  }

  /**
   * 检查是否处于隔离模式
   */
  isInIsolationMode(): boolean {
    return this.state.isIsolated
  }

  /**
   * 获取隔离状态信息
   */
  getIsolationState(): IsolationState {
    return { ...this.state }
  }

  /**
   * 强制结束隔离（用于异常情况）
   */
  forceEndIsolation(): void {
    console.warn('强制结束Cookie隔离模式')
    this.endIsolation()
  }

  /**
   * 设置当前隔离的token（用于调试和监控）
   */
  setCurrentIsolatedToken(token: string): void {
    this.state.currentIsolatedToken = token
  }

  /**
   * 启动账号添加隔离模式
   * 专门用于添加新账号时的Cookie隔离，防止新账号登录污染原始页面Cookie
   */
  startAccountAddIsolation(): void {
    if (this.state.isIsolated) {
      console.warn('Cookie隔离已经启动，跳过重复启动')
      return
    }

    try {
      // 备份当前主账号的Cookie状态
      this.backupMainAccountCookies()

      this.state.isIsolated = true
      this.state.isolationStartTime = Date.now()

      console.log('账号添加Cookie隔离模式已启动')
    }
    catch (error) {
      console.error('启动账号添加Cookie隔离失败:', error)
    }
  }

  /**
   * 结束账号添加隔离模式
   * 在新账号添加完成后调用，恢复主账号Cookie状态
   */
  endAccountAddIsolation(): void {
    if (!this.state.isIsolated) {
      console.warn('Cookie隔离未启动，跳过结束操作')
      return
    }

    try {
      // 恢复主账号Cookie状态
      this.restoreMainAccountCookies()

      this.state.isIsolated = false
      this.state.currentIsolatedToken = null
      this.state.isolationStartTime = 0

      console.log('账号添加Cookie隔离模式已结束，主账号状态已恢复')
    }
    catch (error) {
      console.error('结束账号添加Cookie隔离失败:', error)
      // 如果恢复失败，提供兜底方案
      console.warn('Cookie恢复失败，建议刷新页面以确保状态同步')
    }
  }

  /**
   * 检查隔离是否超时（超过30分钟自动结束）
   */
  checkIsolationTimeout(): void {
    if (this.state.isIsolated && this.state.isolationStartTime > 0) {
      const elapsed = Date.now() - this.state.isolationStartTime
      const timeout = 30 * 60 * 1000 // 30分钟

      if (elapsed > timeout) {
        console.warn('Cookie隔离超时，自动结束隔离模式')
        this.forceEndIsolation()
      }
    }
  }
}

// 创建全局单例
export const cookieIsolationManager = new CookieIsolationManager()

// 定期检查隔离超时
setInterval(() => {
  cookieIsolationManager.checkIsolationTimeout()
}, 60000) // 每分钟检查一次

// 页面卸载时自动结束隔离
window.addEventListener('beforeunload', () => {
  if (cookieIsolationManager.isInIsolationMode())
    cookieIsolationManager.forceEndIsolation()
})

export default cookieIsolationManager
