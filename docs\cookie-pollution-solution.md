# Cookie污染问题解决方案

## 问题背景

在多开模式中添加新账号时，存在Cookie污染问题：

1. **根本原因**：每次调用`loginApi`时，服务器返回`Set-Cookie`响应头，自动设置HttpOnly的Cookie到浏览器
2. **影响范围**：新账号登录成功后，原始页面的Cookie被覆盖，导致登录状态不一致
3. **用户体验**：界面显示的当前账号与实际Cookie中的账号不匹配

## 解决方案架构

### 方案1：最优方案 - 增强Cookie隔离机制

**核心思路**：在添加新账号时启用Cookie隔离，使用专门的隔离API避免Cookie污染。

**实现要点**：
1. **新增隔离模式**：`startAccountAddIsolation()` / `endAccountAddIsolation()`
2. **隔离登录API**：`loginApiIsolated()` 使用`GM_xmlhttpRequest`避免Cookie设置
3. **配置化管理**：通过`CookieIsolationConfig`管理不同场景的隔离策略

**代码示例**：
```typescript
// 启用账号添加隔离
if (shouldEnableIsolation('ADD_ACCOUNT')) {
  cookieIsolationManager.startAccountAddIsolation()
}

// 使用隔离登录API
const res = await loginApiIsolated(account, password, captcha)

// 结束隔离
cookieIsolationManager.endAccountAddIsolation()
```

### 方案2：备选方案 - 恢复原始账号状态

**核心思路**：在新账号添加完成后，使用原始账号的token重新发起请求，恢复原始页面的登录状态。

**实现要点**：
1. **状态恢复**：`restoreOriginalAccountState()` 函数
2. **自动调用**：在登录流程的finally块中自动执行
3. **错误处理**：如果恢复失败，进入兜底方案

**代码示例**：
```typescript
async function restoreOriginalAccountState() {
  const originalToken = getLocalStorage('token')
  if (originalToken) {
    await setTokenApi() // 使用原始token重新设置Cookie
  }
}
```

### 方案3：兜底方案 - 智能硬刷新

**核心思路**：当前两个方案都失败时，根据配置决定是否自动刷新页面。

**实现要点**：
1. **状态检查**：`checkIfRefreshNeeded()` 检测是否需要刷新
2. **配置控制**：根据`shouldAutoRefreshOnFailure()`决定是否自动刷新
3. **用户友好**：提供延迟和提示，避免突然刷新

**代码示例**：
```typescript
if (shouldRefresh && autoRefresh) {
  warningNotify('检测到登录状态异常，即将刷新页面以确保状态同步')
  setTimeout(() => window.location.reload(), 2000)
} else if (shouldRefresh && !autoRefresh) {
  warningNotify('检测到登录状态可能异常，建议手动刷新页面')
}
```

## 配置管理

### 场景配置

```typescript
export const ISOLATION_CONFIGS = {
  // 添加新账号场景
  ADD_ACCOUNT: {
    enabled: true,
    timeoutMs: 5 * 60 * 1000, // 5分钟超时
    autoRefreshOnFailure: true, // 失败时自动刷新
    refreshDelayMs: 2000,
    verboseLogging: true
  },
  
  // 多开抢号场景
  MULTI_ORDER: {
    enabled: true,
    autoRefreshOnFailure: false, // 抢号时不自动刷新
    // ...
  }
}
```

### 使用方式

```typescript
// 检查是否启用隔离
const shouldIsolate = shouldEnableIsolation('ADD_ACCOUNT')

// 检查是否自动刷新
const autoRefresh = shouldAutoRefreshOnFailure('ADD_ACCOUNT')
```

## 技术实现细节

### 1. 隔离登录API

```typescript
export async function loginApiIsolated(account: string, password: string, captcha: GeetestObj) {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      method: 'GET',
      url: loginUrl,
      headers: { 'Content-Type': 'application/json' },
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror: reject
    })
  })
}
```

### 2. Cookie隔离管理器增强

```typescript
class CookieIsolationManager {
  startAccountAddIsolation(): void {
    this.backupMainAccountCookies()
    this.state.isIsolated = true
  }
  
  endAccountAddIsolation(): void {
    this.restoreMainAccountCookies()
    this.state.isIsolated = false
  }
}
```

### 3. 多层防护机制

1. **主防护**：Cookie隔离避免污染
2. **备用防护**：状态恢复修复污染
3. **兜底防护**：智能刷新确保同步

## 使用指南

### 自动使用（推荐）

系统已自动集成，添加新账号时会自动启用Cookie隔离：

```typescript
// 在login函数中自动启用
const shouldIsolate = shouldEnableIsolation('ADD_ACCOUNT')
if (shouldIsolate) {
  cookieIsolationManager.startAccountAddIsolation()
}
```

### 手动控制

如需手动控制，可以调用相关API：

```typescript
// 手动启动隔离
cookieIsolationManager.startAccountAddIsolation()

// 手动结束隔离
cookieIsolationManager.endAccountAddIsolation()

// 手动恢复状态
await restoreOriginalAccountState()
```

## 监控和调试

### 日志输出

- `开始添加新账号，启动Cookie隔离模式`
- `账号添加完成，结束Cookie隔离模式`
- `原始账号状态恢复完成`
- `检测到状态不一致，准备刷新页面...`

### 配置调试

```typescript
// 启用详细日志
ISOLATION_CONFIGS.ADD_ACCOUNT.verboseLogging = true

// 禁用自动刷新（调试时）
ISOLATION_CONFIGS.ADD_ACCOUNT.autoRefreshOnFailure = false
```

## 注意事项

1. **GM_cookie依赖**：Cookie恢复功能依赖Tampermonkey的GM_cookie API
2. **时序控制**：确保隔离的启动和结束时机正确
3. **错误处理**：每个环节都有完善的错误处理和兜底机制
4. **用户体验**：避免频繁刷新，提供清晰的状态提示

## 测试建议

1. **正常流程测试**：添加新账号，验证原始账号状态不受影响
2. **异常情况测试**：模拟Cookie隔离失败，验证恢复机制
3. **配置测试**：测试不同配置下的行为差异
4. **并发测试**：测试多个账号同时添加的情况
